export type { IDeleteEntry } from "./IDeleteEntry.js";
export type { IDeleteEntryOperation } from "./IDeleteEntryOperation.js";
export type { IGetEntriesByIds } from "./IGetEntriesByIds.js";
export type { IGetEntry } from "./IGetEntry.js";
export type { IGetLatestEntriesByIds } from "./IGetLatestEntriesByIds.js";
export type { IGetLatestRevisionByEntryId } from "./IGetLatestRevisionByEntryId.js";
export type { IGetPreviousRevisionByEntryId } from "./IGetPreviousRevisionByEntryId.js";
export type { IGetPublishedEntriesByIds } from "./IGetPublishedEntriesByIds.js";
export type { IGetPublishedRevisionByEntryId } from "./IGetPublishedRevisionByEntryId.js";
export type { IGetRevisionById } from "./IGetRevisionById.js";
export type { IGetRevisionsByEntryId } from "./IGetRevisionsByEntryId.js";
export type { IListEntries } from "./IListEntries.js";
export type { IListEntriesOperation } from "./IListEntriesOperation.js";
export type { IMoveEntryToBinOperation } from "./IMoveEntryToBinOperation.js";
export type { IRestoreEntryFromBin } from "./IRestoreEntryFromBin.js";
export type { IRestoreEntryFromBinOperation } from "./IRestoreEntryFromBinOperation.js";
