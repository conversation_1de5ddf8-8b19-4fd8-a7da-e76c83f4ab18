{"extends": "../../tsconfig.build.json", "include": ["src"], "references": [{"path": "../api/tsconfig.build.json"}, {"path": "../api-elasticsearch/tsconfig.build.json"}, {"path": "../api-elasticsearch-tasks/tsconfig.build.json"}, {"path": "../api-headless-cms/tsconfig.build.json"}, {"path": "../aws-sdk/tsconfig.build.json"}, {"path": "../db-dynamodb/tsconfig.build.json"}, {"path": "../error/tsconfig.build.json"}, {"path": "../handler-db/tsconfig.build.json"}, {"path": "../plugins/tsconfig.build.json"}, {"path": "../utils/tsconfig.build.json"}, {"path": "../api-dynamodb-to-elasticsearch/tsconfig.build.json"}, {"path": "../api-i18n/tsconfig.build.json"}, {"path": "../api-log/tsconfig.build.json"}, {"path": "../api-security/tsconfig.build.json"}, {"path": "../api-tenancy/tsconfig.build.json"}, {"path": "../api-wcp/tsconfig.build.json"}, {"path": "../handler/tsconfig.build.json"}, {"path": "../handler-aws/tsconfig.build.json"}, {"path": "../handler-graphql/tsconfig.build.json"}, {"path": "../tasks/tsconfig.build.json"}], "compilerOptions": {"rootDir": "./src", "outDir": "./dist", "declarationDir": "./dist", "paths": {"~/*": ["./src/*"], "~tests/*": ["./__tests__/*"]}, "baseUrl": "."}}