{"extends": "../../tsconfig.json", "include": ["src", "__tests__"], "references": [{"path": "../error"}, {"path": "../plugins"}], "compilerOptions": {"rootDirs": ["./src", "./__tests__"], "outDir": "./dist", "declarationDir": "./dist", "paths": {"~/*": ["./src/*"], "~tests/*": ["./__tests__/*"], "@webiny/error/*": ["../error/src/*"], "@webiny/error": ["../error/src"], "@webiny/plugins/*": ["../plugins/src/*"], "@webiny/plugins": ["../plugins/src"]}, "baseUrl": "."}}