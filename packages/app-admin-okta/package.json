{"name": "@webiny/app-admin-okta", "version": "0.0.0", "type": "module", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/webiny/webiny-js.git"}, "description": "Identity provider plugin for AWS Cognito", "author": "Webiny Ltd.", "license": "Webiny Enterprise", "dependencies": {"@apollo/react-hooks": "^3.1.5", "@emotion/styled": "11.10.6", "@okta/okta-auth-js": "^5.11.0", "@okta/okta-react": "^6.10.0", "@okta/okta-signin-widget": "^5.16.1", "@webiny/admin-ui": "0.0.0", "@webiny/app": "0.0.0", "@webiny/app-admin": "0.0.0", "@webiny/app-security": "0.0.0", "@webiny/app-serverless-cms": "0.0.0", "@webiny/app-tenant-manager": "0.0.0", "@webiny/form": "0.0.0", "@webiny/icons": "0.0.0", "@webiny/plugins": "0.0.0", "@webiny/ui": "0.0.0", "@webiny/validation": "0.0.0", "apollo-client": "^2.6.10", "apollo-link-context": "^1.0.20", "emotion": "10.0.27", "graphql": "^16.11.0", "graphql-tag": "^2.12.6", "lodash": "^4.17.21", "react": "18.2.0", "react-dom": "18.2.0", "react-helmet": "^6.1.0"}, "devDependencies": {"@webiny/build-tools": "0.0.0", "rimraf": "^6.0.1", "typescript": "5.9.3"}, "publishConfig": {"access": "public", "directory": "dist"}, "adio": {"ignore": {"peerDependencies": ["react-dom"]}}}