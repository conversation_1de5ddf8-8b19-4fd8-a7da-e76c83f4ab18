#
# This is the default config file. It allows all users to do anything,
# so don't use it on production systems.
#
# Look here for more config file examples:
# https://github.com/verdaccio/verdaccio/tree/master/conf
#

# path to a directory with all packages
storage: ./.verdaccio

# path to a directory with plugins to include
plugins: ./plugins

web:
  # WebUI is enabled as default, if you want disable it, just uncomment this line
  #enable: false
  title: Verd<PERSON>cio

auth:
  htpasswd:
    file: ./htpasswd
    # Maximum amount of users allowed to register, defaults to "+inf".
    # You can set this to -1 to disable registration.
    #max_users: 1000

# a list of other known repositories we can talk to
uplinks:
  npmjs:
    url: https://registry.npmjs.org/

packages:
  "@*/*":
    # scoped packages
    access: $all
    publish: $all
    proxy: npmjs

  "**":
    access: $all
    publish: $all
    proxy: npmjs

# You can specify HTTP/1.1 server keep alive timeout in seconds for incomming connections.
# A value of 0 makes the http server behave similarly to Node.js versions prior to 8.0.0, which did not have a keep-alive timeout.
# WORKAROUND: Through given configuration you can workaround following issue https://github.com/verdaccio/verdaccio/issues/301. Set to 0 in case 60 is not enought.
server:
  keepAliveTimeout: 60

# To use `npm audit` uncomment the following section
middlewares:
  audit:
    enabled: true

logs:
  - { type: stdout, format: pretty, level: http }

max_body_size: 1000mb
